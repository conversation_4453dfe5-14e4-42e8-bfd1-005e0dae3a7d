import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>B<PERSON>,
  <PERSON>,
  Separator,
  Text,
  UserMenu as UserProfile,
  useModalControl,
  ModalClose
} from "@snap/design-system";
import { formatIsoDate, getInitials } from "~/helpers";
import { USER_CONSTANTS } from "~/helpers/constants";
import { useUserData } from "~/store/userStore";
import { Too<PERSON>ip, TooltipTrigger, TooltipContent } from "./ui/tooltip";
import { Info } from "lucide-react";

interface UserMenuProps {
  userMenuContent: React.ReactNode;
}

export default function UserMenu({ userMenuContent }: UserMenuProps) {
  const { open } = useModalControl();
  const userData = useUserData();
  const userImage = userData?.[USER_CONSTANTS.user_data.image as keyof typeof userData] as string;
  const userName = userData?.[USER_CONSTANTS.user_data.name as keyof typeof userData] as string;
  const userCredits = userData?.[USER_CONSTANTS.user_data.credits_minimun as keyof typeof userData] as number;
  const initials = getInitials(userName);
  const userCreditsResetDate = userData?.[USER_CONSTANTS.user_data.next_reset_credits as keyof typeof userData] as string;
  const userProfile = userData?.[USER_CONSTANTS.user_data.role as keyof typeof userData] as string;
  const isUserStandalone = userProfile === "standalone";

  const getValidImageUrl = (image: string | null | undefined) => {
    return image || undefined;
  };

  const renderConsultasInfo = () => {
    return (
      <Tooltip delayDuration={100}>
        <TooltipTrigger className="cursor-help" asChild>
          <Info size={14} className="text-accent" />
        </TooltipTrigger>
        <TooltipContent side="bottom" className="!p-0 !bg-transparent !shadow-none !border-none max-w-[288px]">
          <ChamferBox corner="topLeft" className="rounded-md px-0.5 pt-0.5 bg-neutral-800">
            <div className="relative z-10">
              <Text className="text-left text-netral-100">
                Você possui <span className="font-bold text-accent">{`* ${userCredits} consultas `}</span>
                válidas até a data <span className="font-semibold text-accent">{formatIsoDate(userCreditsResetDate, true)}</span>.
              </Text>
              {
                isUserStandalone && (
                  <div>
                    <Separator className="my-2 border-border" />
                    <Text className="text-left text-neutral-200">
                      <span className="font-bold text-accent">*</span> {`Saiba mais sobre o cálculo de consultas disponíveis `}
                      <span className="text-accent underline cursor-pointer" onClick={handleOpenConsultasInfo} title="Abrir mais informações de consultas">
                        clicando aqui.
                      </span>
                    </Text>

                  </div>
                )
              }
            </div>
          </ChamferBox>
        </TooltipContent>
      </Tooltip>
    );
  };

  const handleOpenConsultasInfo = () => {
    open({
      modal: () => ({
        title: "INFORMAÇÕES SOBRE CÁLCULO DE CONSULTAS",
        content: <div className="space-y-4 text-neutral-200">
          <Text variant="body-md" className="text-left">
            As consultas mensais são compartilhadas entre todos os usuários da <span className="font-bold text-accent">organização</span>.
          </Text>
          <Text variant="body-md" className="text-left">
            Seu saldo de consultas disponíveis depende da forma como a organização optou por distribuir as consultas.
          </Text>

          <Text variant="body-md" className="text-left mt-1 font-bold">
            Exemplo:
          </Text>
          <ul className="list-disc list-inside ml-4">
            <li>
              Organização: limite de 100 consultas/mês
            </li>
            <li>
              <strong>Cenário A</strong> - Todos usuários recebem 100 consultas.
              Se outro usuário realizar 1 consulta, seu saldo ficará de consultas 99.
            </li>
            <li>
              <strong>Cenário B</strong> - São distribuídas cotas de 10 consultas entre 10 usuários:
              seu saldo diminui de acordo com o seu uso, já que o mínimo entre as consultas disponíveis e o limite da organização é sempre considerado.
            </li>
          </ul>
        </div>,
        footer: <ModalClose><Button>Fechar</Button></ModalClose>,
      }),
      config: {
        content: {
          className: "max-w-xl",
        },
      },
    });
  };

  const renderUserProfile = () => {
    const profileProps = {
      Profile: (
        <div className="flex items-center gap-3 w-full">
          <Avatar
            size="sm"
            className="size-9"
            src={getValidImageUrl(userImage)}
            fallback={initials || "NA"}
            textAlign="left"
          />
          <List className="space-y-0.5 items-start">
            <span className="text-sm leading-tight">
              {userName || "Sem nome"}
            </span>
            <Separator />
            <div className="flex items-end gap-2">
              <Text className="opacity-80">
                {`${userCredits || 0} Consultas`}
              </Text>
              {renderConsultasInfo()}
            </div>
          </List>
        </div>
      ),
      Menu: userMenuContent,
    };

    return <UserProfile {...profileProps} menuClassName="py-0 px-0" />;
  };

  return renderUserProfile();
}
